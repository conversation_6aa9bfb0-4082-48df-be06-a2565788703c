
const moneyHud = Vue.createApp({
    data() {
        return {
            bank: 0,
            amount: 0,
            plus: false,
            minus: false,
            showBank: false,
            showUpdate: false,
            updateTimeout: null,
            bankTimeout: null,
            showTimeout: null,
            cleanupInterval: null,
            emergencyCleanup: null,
        };
    },
    destroyed() {
        window.removeEventListener("message", this.listener);
        if (this.updateTimeout) clearTimeout(this.updateTimeout);
        if (this.bankTimeout) clearTimeout(this.bankTimeout);
        if (this.showTimeout) clearTimeout(this.showTimeout);
        if (this.cleanupInterval) clearInterval(this.cleanupInterval);
        if (this.emergencyCleanup) clearTimeout(this.emergencyCleanup);
    },
    mounted() {
        this.listener = window.addEventListener("message", (event) => {
            try {

                if (!event.data || !event.data.action) {
                    console.warn('Invalid message data received:', event.data);
                    return;
                }

                switch (event.data.action) {
                    case "showconstant":
                        this.showConstant(event.data);
                        break;
                    case "update":
                        this.update(event.data);
                        break;
                    case "show":
                        this.showAccounts(event.data);
                        break;
                    case "hide":
                        this.hideAll();
                        break;
                    default:
                        console.warn('Unknown action:', event.data.action);
                }
            } catch (error) {
                console.error('Error processing message:', error, event.data);
            }
        });
    },
    methods: {

        formatMoney(value) {
            try {

                const numValue = Number(value) || 0;

                if (!isFinite(numValue)) {
                    return "$0";
                }

                const formatter = new Intl.NumberFormat("en-US", {
                    style: "currency",
                    currency: "USD",
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 0,
                });
                return formatter.format(numValue);
            } catch (error) {
                console.error('Error formatting money:', error, value);
                return "$0";
            }
        },
        showConstant(data) {
            this.clearAllTimeouts();

            this.showBank = true;
            this.bank = Number(data.bank) || 0;
            this.showUpdate = false;
            this.plus = false;
            this.minus = false;

            // إضافة timeout للإخفاء التلقائي حتى لو كان constant
            this.bankTimeout = setTimeout(() => {
                this.showBank = false;
                this.$forceUpdate();
            }, 5000); // 5 ثواني للعرض المستمر

            // تشغيل cleanup الطوارئ
            this.startEmergencyCleanup();
        },
        update(data) {

            this.clearAllTimeouts();

            this.amount = Number(data.amount) || 0;
            this.bank = Number(data.bank) || 0;
            this.minus = Boolean(data.minus);
            this.plus = Boolean(data.plus);

            if (data.type === "bank") {
                this.showBank = true;
                this.showUpdate = true;

                this.updateTimeout = setTimeout(() => {
                    this.showUpdate = false;
                    this.plus = false;
                    this.minus = false;
                    this.$forceUpdate();
                }, 1500);

                this.bankTimeout = setTimeout(() => {
                    this.showBank = false;
                    this.$forceUpdate();
                }, 3000);

                this.startSmartCleanup();
                this.startEmergencyCleanup();
            }
        },

        clearAllTimeouts() {
            if (this.updateTimeout) {
                clearTimeout(this.updateTimeout);
                this.updateTimeout = null;
            }
            if (this.bankTimeout) {
                clearTimeout(this.bankTimeout);
                this.bankTimeout = null;
            }
            if (this.showTimeout) {
                clearTimeout(this.showTimeout);
                this.showTimeout = null;
            }
            if (this.cleanupInterval) {
                clearInterval(this.cleanupInterval);
                this.cleanupInterval = null;
            }
            if (this.emergencyCleanup) {
                clearTimeout(this.emergencyCleanup);
                this.emergencyCleanup = null;
            }
        },

        forceHideUpdate() {
            this.showUpdate = false;
            this.plus = false;
            this.minus = false;
            this.$forceUpdate();
        },

        startSmartCleanup() {
            if (this.cleanupInterval) return;

            this.cleanupInterval = setTimeout(() => {
                // فحص العناصر المعلقة وإخفاؤها
                if (this.showUpdate && !this.updateTimeout) {
                    console.log('Found stuck update message, forcing hide');
                    this.forceHideUpdate();
                }

                // فحص البنك المعلق وإخفاؤه
                if (this.showBank && !this.bankTimeout && !this.showTimeout) {
                    console.log('Found stuck bank display, forcing hide');
                    this.showBank = false;
                    this.$forceUpdate();
                }

                this.cleanupInterval = null;
            }, 2000);
        },
        showAccounts(data) {
            if (data.type === "bank") {

                if (this.showTimeout) {
                    clearTimeout(this.showTimeout);
                    this.showTimeout = null;
                }

                this.showBank = true;
                this.bank = Number(data.bank) || 0;

                if (this.showUpdate) {
                    this.forceHideUpdate();
                }

                this.showTimeout = setTimeout(() => {
                    this.showBank = false;
                    this.$forceUpdate();
                }, 3500);

                // تشغيل cleanup الطوارئ
                this.startEmergencyCleanup();
            }
        },
        hideAll() {
            this.clearAllTimeouts();

            this.showBank = false;
            this.showUpdate = false;
            this.plus = false;
            this.minus = false;
        },

        // دالة طوارئ للإخفاء التلقائي
        startEmergencyCleanup() {
            // إلغاء أي cleanup سابق
            if (this.emergencyCleanup) {
                clearTimeout(this.emergencyCleanup);
            }

            // تشغيل cleanup طوارئ بعد 10 ثواني
            this.emergencyCleanup = setTimeout(() => {
                console.log('Emergency cleanup: forcing hide all elements');
                this.showBank = false;
                this.showUpdate = false;
                this.plus = false;
                this.minus = false;
                this.$forceUpdate();
                this.emergencyCleanup = null;
            }, 10000); // 10 ثواني كحد أقصى
        },
    },
}).mount("#money-container");